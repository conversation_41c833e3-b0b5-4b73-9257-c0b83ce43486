<?php
/**
 *  +----------------------------------------------------------------------
 *  | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 *  +----------------------------------------------------------------------
 *  | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
 *  +----------------------------------------------------------------------
 *  | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 *  +----------------------------------------------------------------------
 *  | Author: CRMEB Team <<EMAIL>>
 *  +----------------------------------------------------------------------
 */

namespace app\dao\system;


use app\dao\BaseDao;
use app\model\system\SystemRouteCate;

/**
 * Class SystemRouteCateDao
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023/4/6
 * @package app\dao\system
 */
class SystemRouteCateDao extends BaseDao
{

    /**
     * @return string
     * <AUTHOR>
     * @email <EMAIL>
     * @date 2023/4/6
     */
    protected function setModel(): string
    {
        return SystemRouteCate::class;
    }
}
