﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="ClassDiagrams.PHPExcel_Reader_Excel2007" Collapsed="true" BaseTypeListCollapsed="true">
    <Position X="0.5" Y="3.5" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Classes\PHPExcel_Reader_Excel2007.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="ClassDiagrams.PHPExcel_Writer_Excel2007" Collapsed="true" BaseTypeListCollapsed="true">
    <Position X="3.5" Y="3.5" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAA=</HashCode>
      <FileName>Classes\PHPExcel_Writer_Excel2007.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="ClassDiagrams.PHPExcel_Reader_Serialized" Collapsed="true" BaseTypeListCollapsed="true">
    <Position X="0.5" Y="4.25" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Classes\PHPExcel_Reader_Serialized.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="ClassDiagrams.PHPExcel_Writer_Serialized" Collapsed="true" BaseTypeListCollapsed="true">
    <Position X="3.5" Y="4.25" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAA=</HashCode>
      <FileName>Classes\PHPExcel_Writer_Serialized.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="ClassDiagrams.PHPExcel_Reader_CSV" Collapsed="true" BaseTypeListCollapsed="true">
    <Position X="0.5" Y="5" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Classes\PHPExcel_Reader_Serialized.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="ClassDiagrams.PHPExcel_Writer_CSV" Collapsed="true" BaseTypeListCollapsed="true">
    <Position X="3.5" Y="5" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAA=</HashCode>
      <FileName>Classes\PHPExcel_Writer_Serialized.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="ClassDiagrams.PHPExcel_Writer_Excel5" Collapsed="true" BaseTypeListCollapsed="true">
    <Position X="3.5" Y="5.75" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAA=</HashCode>
      <FileName>Classes\PHPExcel_Writer_Serialized.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="ClassDiagrams.PHPExcel_Writer_HTML" Collapsed="true" BaseTypeListCollapsed="true">
    <Position X="3.5" Y="6.5" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAA=</HashCode>
      <FileName>Classes\PHPExcel_Writer_Serialized.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="ClassDiagrams.PHPExcel_Reader_Excel5" Collapsed="true">
    <Position X="0.5" Y="5.75" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Classes\PHPExcel_Reader_Excel5.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="ClassDiagrams.PHPExcel_Writer_PDF" Collapsed="true">
    <Position X="3.5" Y="7.25" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAA=</HashCode>
      <FileName>Classes\PHPExcel.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="ClassDiagrams.PHPExcel_IOFactory">
    <Position X="2" Y="0.5" Width="2.25" />
    <AssociationLine Name="createsReader" Type="ClassDiagrams.PHPExcel_Reader_IReader">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.11" Y="0.06" />
      </MemberNameLabel>
    </AssociationLine>
    <AssociationLine Name="createsWriter" Type="ClassDiagrams.PHPExcel_Writer_IWriter">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.088" Y="0.081" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AIAAAAAAAAEAAAAAAQAAAAAAAAAAAAAAAAABAAAAAAA=</HashCode>
      <FileName>Classes\PHPExcel_IOFactory.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="createsReader" />
      <Property Name="createsWriter" />
    </ShowAsAssociation>
  </Class>
  <Class Name="ClassDiagrams.PHPExcel_Reader_Excel2003XML" Collapsed="true">
    <Position X="0.5" Y="6.5" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Classes\PHPExcel_Reader_Excel5.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="ClassDiagrams.PHPExcel_Reader_SYLK" Collapsed="true">
    <Position X="0.5" Y="7.25" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Classes\PHPExcel_Reader_Excel5.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Interface Name="ClassDiagrams.PHPExcel_Writer_IWriter" Collapsed="true">
    <Position X="3.5" Y="2.5" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAA=</HashCode>
      <FileName>Classes\IWriter.cs</FileName>
    </TypeIdentifier>
  </Interface>
  <Interface Name="ClassDiagrams.PHPExcel_Reader_IReader" Collapsed="true">
    <Position X="0.5" Y="2.5" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>Classes\IReader.cs</FileName>
    </TypeIdentifier>
  </Interface>
  <Font Name="Tahoma" Size="8.25" />
</ClassDiagram>