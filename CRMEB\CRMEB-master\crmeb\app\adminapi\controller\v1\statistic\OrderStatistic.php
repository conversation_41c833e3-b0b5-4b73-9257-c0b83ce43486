<?php

namespace app\adminapi\controller\v1\statistic;

use app\adminapi\controller\AuthController;
use app\services\statistic\OrderStatisticServices;
use think\facade\App;

class OrderStatistic extends AuthController
{
    public function __construct(App $app, OrderStatisticServices $services)
    {
        parent::__construct($app);
        $this->services = $services;
    }

    /**
     * 订单统计基础信息
     * @return mixed
     */
    public function getBasic()
    {
        $where = $this->request->getMore([
            ['time', '']
        ]);
        $data = $this->services->getBasic($where);
        return app('json')->success($data);
    }

    /**
     * 订单统计趋势图
     * @return mixed
     */
    public function getTrend()
    {
        $where = $this->request->getMore([
            ['time', '']
        ]);
        $data = $this->services->getTrend($where);
        return app('json')->success($data);
    }

    /**
     * 订单来源
     * @return mixed
     */
    public function getChannel()
    {
        $where = $this->request->getMore([
            ['time', '']
        ]);
        $data = $this->services->getChannel($where);
        return app('json')->success($data);
    }

    /**
     * 订单类型
     * @return mixed
     */
    public function getType()
    {
        $where = $this->request->getMore([
            ['time', '']
        ]);
        $data = $this->services->getType($where);
        return app('json')->success($data);
    }
}